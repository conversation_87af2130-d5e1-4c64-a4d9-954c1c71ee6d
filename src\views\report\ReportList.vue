<template>
    <div class="height-100 back-color-common relative"></div>
    <div v-if="token" class="height-100 back-transparent absolute top-0 overflow-y-auto">
        <div class="content back-transparent">
            <!-- 搜索区域 -->
            <div class="display-flex space-between top-bottom-center">
                <van-search v-model="searchValue" placeholder="请输入您购买报告的企业名称" background="#00000000" style="width: 90%;"/>
                <div class="display-flex flex-column top-bottom-center" @click="searchFilterShow = !searchFilterShow">
                    <Icon icon="icon-Frame6" size="16" color="#252525"/>
                    <div class="font-10 color-black">筛选</div>
                </div>
            </div>
            <!-- 采集状态 -->
            <!-- 列表区域 -->
            <van-list v-model:loading="listLoading" :finished="listFinished" finished-text="没有更多了" @load="onLoad">
                <div class="list-card" v-for="(item, index) in list" :key="index">
                    <div class="display-flex top-bottom-center b-margin-8">
                        <div
                            class="w-46 h-46 border-radius-4 back-color-blue color-white font-14 r-margin-8 border-box tb-padding-3 lr-padding-8"
                        >
                            {{ item.companyName.slice(0, 4) }}
                        </div>
                        <div class="color-two-grey font-14 flex-1">
                            <div class="color-black font-16 b-margin-2 maxw-250 text-ellipsis text-nowrap">
                                {{ item.companyName }}
                            </div>
                            <div class="b-margin-2">{{ item.socialCreditCode }}</div>
                        </div>
                    </div>
                    <div class="b-margin-8 font-14 !color-blue display-flex top-bottom-center">
                        <Icon :icon="item.collectType === 'INVOICE' ? 'icon-Frame1' : (item.collectType === 'TAX' ? 'icon-Frame3' : '')" color="#3C74EB" size="16"></Icon>
                        <div class="l-margin-2">{{ item.collectType === 'INVOICE' ? '企业发票数据综合分析报告' : '企业财税经营分析报告' }}</div>
                    </div>
                    <div class="color-two-grey font-12 b-margin-4">创建时间：{{ parseTime(item.createTime, '{y}-{m}-{d} {h}:{i}:{s}') || '-' }}</div>
                    <div class="color-two-grey font-12 b-margin-8">执行时间：{{ parseTime(item.executionTime, '{y}-{m}-{d} {h}:{i}:{s}') || '-' }}</div>
                    <div class="t-padding-8 display-flex space-between top-bottom-center font-12 border-t">
                        <div :class="item.status">
                            {{ reportStatus.find((aa) => aa.value === item.status)?.label || '-' }}
                        </div>
                        <div>
                            <SendReportEmail v-if="item.status === 'SUCCESS'" :item="item"></SendReportEmail>
                            <Icon v-else icon="icon-mail-03" size="25" color="#9EBAF5"></Icon>
                        </div>
                    </div>
                </div>
            </van-list>
            <van-popup
                v-model:show="searchFilterShow"
                close-icon-position="top-left"
                position="bottom"
                :close-on-click-overlay="false"
                :style="{ height: '9rem' }"
            >
                <template #default>
                    <div class="lr-padding-16">
                        <div class="font-16 color-black text-center t-padding-24 relative">
                            筛选条件
                            <div class="absolute top-24 right-0" @click="closeFilter()">
                                <Icon icon="icon-x-02" color="#252525" size="24" />
                            </div>
                        </div>
                        <div class="t-margin-24 font-14 color-black b-margin-12">采集状态</div>
                        <div class="display-flex flex-wrap space-between">
                            <div
                                v-for="item in reportStatus"
                                :key="item.value"
                                class="color-two-grey font-14 border-tag tb-padding-8 lr-padding-30 b-margin-8 border-radius-4 back-tag-bg boder-none"
                                :class="{ active: choosedClickStatus === item.value }"
                                @click="handleClickStatus(item.value)"
                            >
                                {{ item.label }}
                            </div>
                        </div>
                        <div class="t-margin-8 font-14 color-black b-margin-12">采集报告</div>
                        <div class="display-flex flex-wrap space-between">
                            <div
                                v-for="item in reportType"
                                :key="item.value"
                                class="color-two-grey font-14 border-tag tb-padding-8 lr-padding-30 border-radius-4 back-tag-bg boder-none"
                                :class="{ active: choosedCollectType === item.value }"
                                @click="handleClickCollectType(item.value)"
                            >
                                {{ item.label }}
                            </div>
                        </div>
                        <div class="t-margin-32">
                            <van-button type="primary" class="width-100 h-46 border-radius-8" style="background: linear-gradient(to right, #3c74eb, #95d5f4); border: none;" @click="filterSearch()">确定</van-button>
                        </div>

                    </div>
                </template>
            </van-popup>
        </div>
    </div>
    <div v-else class="absolute height-100 width-100 font-16 display-flex center top-0 left-0">
        <van-button type="primary" class="h-46 width-80" style="background: linear-gradient(to right, #3c74eb, #95d5f4); border: none; border-radius: 8px" @click="handleTo()">去登录</van-button>
    </div>
</template>

<script lang="ts" setup>
import { ref, watch, computed } from 'vue'
import reportService from '@/service/reportService'
import type { CollectLogParams, SearchCollectLogItem } from '@/types/report'
import { parseTime } from '@/utils/parse-time'
import { useRouter } from 'vue-router'
import { tabbarheight } from '@/utils/tabbar-height'
import Icon from '@/components/common/Icon.vue'
import SendReportEmail from '@/views/report/components/SendReportEmail.vue'

const paddingBottom = computed(() => {
    return tabbarheight() + 'px'
})
const router = useRouter()
const token = localStorage.getItem('access_token')
const queryParams = ref<CollectLogParams>({
    page: 0,
    pageSize: 10,
    status: '',
    collectType:''
})
const searchValue = ref()
const list = ref<SearchCollectLogItem[]>([])
const listLoading = ref(false)
const listFinished = ref(false)
const searchFilterShow = ref(false)
const reportStatus = [
    {
        label: '全部状态',
        value: '',
    },
    {
        label: '采集成功',
        value: 'SUCCESS',
    },
    {
        label: '采集中',
        value: 'COLLECTING',
    },
    {
        label: '采集失败',
        value: 'ERROR',
    }
]
const reportType = [
    {
        label: '全部报告',
        value: ''
    },
    {
        label: '发票报告',
        value: 'INVOICE'
    },
    {
        label: '财税报告',
        value: 'TAX'
    }
    

]
const choosedClickStatus = ref<string>('')
const choosedCollectType = ref<string>('')

let timeoutId: number | null = null
watch(
    () => searchValue.value,
    (newVal) => {
        if (timeoutId) {
            clearTimeout(timeoutId)
        }
        timeoutId = window.setTimeout(() => {
            console.log('newVal', newVal)
            if (newVal) {
                queryParams.value.page = 0
                queryParams.value.companyName = newVal
            } else {
                queryParams.value = {
                    page: 0,
                    pageSize: 10,
                }
            }
            list.value = []
            listFinished.value = false
            onLoad()
        }, 500)
    }
)
watch(() => searchFilterShow.value,
    (newVal) => {
        if (newVal) {
            choosedClickStatus.value = queryParams.value.status as string
            choosedCollectType.value = queryParams.value.collectType as string
        }
    }
)
let onLoadTimer: number | null = null
const onLoad = async () => {
    if (onLoadTimer) {
        clearTimeout(onLoadTimer)
    }

    onLoadTimer = window.setTimeout(async () => {
        queryParams.value.page = (queryParams.value.page ?? 0) + 1
        let res = await reportService.collectPage(queryParams.value)
        console.log('res', res)
        if (res.errCode === 0) {
            listLoading.value = false
            const { data, total } = res
            if (data && data.length > 0) {
                list.value = list.value.concat(data)
            }

            if (list.value.length >= total) {
                listFinished.value = true
            }
        }
    }, 500)
}
const handleClickStatus = (val: string) => {
    choosedClickStatus.value = val
}
const handleClickCollectType = (val:string) => {
    choosedCollectType.value = val
}
const closeFilter = () => {
    searchFilterShow.value = false
    
}
const filterSearch = () => {
    list.value = []
    listFinished.value = false
    queryParams.value = {
        page: 0,
        pageSize: 10,
        status: choosedClickStatus.value,
        collectType: choosedCollectType.value
    }
    onLoad()
    searchFilterShow.value = false
}
const handleTo = () => {
    router.push({
        name: 'passwordLogin',
    })
}
</script>

<style lang="scss" scoped>
.content {
    width: 100vw;
    min-height: 100vh;
    padding: 0 16px;
    box-sizing: border-box;
    padding-bottom: v-bind(paddingBottom);
    :deep(.van-search) {
        padding: 12px 0;
    }
    :deep(.van-search__content) {
        background-color: #fff;
    }
}
.list-card {
    // height: 127px;
    padding: 8px 16px;
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 8px;
    box-sizing: border-box;
}

.border-r {
    border-right: 1px solid rgba(38, 38, 38, 0.06);
}
.border-t {
    border-top: 1px solid rgba(38, 38, 38, 0.06);
}
.active {
    background-color: #e9f0fe;
    color: var(--main-black);
}
.SUCCESS {
    color: var(--main-green-);
}
.ERROR {
    color: var(--main-red-);
}
.WAITING {
    color: var(--main-blue-);
}
.COLLECTING {
    color: var(--main-blue-);
}

</style>
