<script lang="ts" setup>
import orderService from '@/service/orderService'
import type { IOrderPaymentListItem, IOrderPrepPayparams } from '@/types/order'
import { closeToast, showFailToast, showLoadingToast, showSuccessToast } from 'vant'
import { ref } from 'vue'
import { useRouter } from 'vue-router'
defineOptions({
    name: 'orderList',
})

const props = defineProps<{
    type: '' | '0' | '4' | '1' | '2'
}>()

const list = ref<IOrderPaymentListItem[]>([])
const loading = ref(false)
const finished = ref(false)
const page = ref(1)
const pageSize = ref(10)
const total = ref(0)
const refreshing = ref(false)
const router = useRouter()

const onLoad = () => {
    if (refreshing.value) {
        page.value = 1
        finished.value = false
        refreshing.value = false
    }
    getList()
}

const getList = () => {
    orderService
        .goodsOrderList({
            page: page.value,
            pageSize: pageSize.value,
            orderStatus: props.type,
        })
        .then((res) => {
            // loading.value = false
            if (res.errCode === 0) {
                if (page.value === 1) {
                    list.value = res.data
                } else {
                    // 否则合并数据，并去重（假设每条数据有唯一的id字段）
                    const newData = res.data.filter(
                        (item) => !list.value.some((existingItem) => existingItem.id === item.id)
                    )
                    list.value = [...list.value, ...newData]
                }

                // 更新总数据量
                total.value = res.total

                // 检查是否还有更多数据
                // if (res.lastPage || res.data.length < pageSize.value) {
                //     finished.value = true
                // } else {
                //     page.value++
                // }
                if (list.value.length === total.value) {
                    finished.value = true
                } else {
                    page.value = page.value + 1
                }
            }
        })
        .catch((error) => {
            console.error('加载数据失败:', error)
            loading.value = false
            finished.value = true
        })
}

const formatPrice = (price: number) => {
    return (price / 100).toFixed(2)
}

const onRefresh = () => {
    refreshing.value = true
    onLoad()
}

const cancelOrder = (orderId: string) => {
    showLoadingToast('正在取消订单...')
    orderService
        .goodsOrderCancel({
            orderId: orderId,
        })
        .then((res) => {
            closeToast()
            const { errCode, errMsg } = res
            if (errCode === 0) {
                showSuccessToast('订单已取消')
                const newList: IOrderPaymentListItem[] = JSON.parse(JSON.stringify(list.value))
                const targetIndex = newList.findIndex((e) => e.out_order_id === orderId)
                newList[targetIndex].order_status = '4'
                newList[targetIndex].order_status_str = '已取消'
                list.value = newList
            } else {
                showFailToast(errMsg || '订单取消失败')
            }

            getOrderDetail(orderId)
        })
        .catch(() => {
            showFailToast('系统错误，请稍后再试')
        })
}

const getOrderDetail = (orderId: string) => {
    if (!orderId) return
    orderService
        .goodsOrderDetail({
            orderId: orderId,
        })
        .then((res) => {
            const { errCode, data } = res
            if (errCode === 0) {
                const targetIndex = list.value.findIndex((e) => e.out_order_id === orderId)
                list.value[targetIndex].order_status = data.order_status
                list.value[targetIndex].order_status_str = data.order_status_str
            }
        })
}

const isCanCancel = (status: string) => {
    return status === '0'
}

const isCanRepay = (status: string) => {
    return status === '0'
}

const toDetail = (orderId: string) => {
    if (!orderId) return

    router.push({
        name: 'myOrdersDetail',
        query: {
            orderId: orderId,
        },
    })
}

const rePay = (orderId: string) => {
    showLoadingToast({
        message: '即将打开支付页面...',
        forbidClick: true,
    })

    orderService
        .goodsOrderRepay({
            orderId: orderId,
        })
        .then((res) => {
            const { errCode, errMsg } = res

            if (errCode === 0) {
                console.log('createOrder 成功')
                doPay(res.data)
            } else {
                showFailToast(errMsg || '无法打开支付页面，请稍后再试')
            }
        })
        .finally(() => {
            closeToast()
        })
}

const doPay = (data: IOrderPrepPayparams) => {
    const successUrl = encodeURIComponent(`${location.href}`)

    let url = 'http://weixin.shuzutech.com/jsapi-pay.html?'
    url = url + `appId=${data.appId}`
    url = url + `&timeStamp=${data.timeStamp}`
    url = url + `&nonceStr=${data.nonceStr}`
    url = url + `&package=${data.package}`
    url = url + `&signType=${data.signType}`
    url = url + `&paySign=${data.paySign}`
    url = url + `&successUrl=${successUrl}`

    window.location.href = url
}
</script>

<template>
    <div class="order-list tb-padding-12 lr-padding-16">
        <van-pull-refresh v-model="loading" @refresh="onRefresh">
            <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
                <div
                    class="tb-padding-8 lr-padding-16 back-color-white border-radius-8 b-margin-12 flex flex-column gap-8"
                    v-for="item in list"
                    :key="item.id"
                    @click="toDetail(item.out_order_id)"
                >
                    <div class="flex flex-row space-between">
                        <div class="font-14 lh-20">订单编号：{{ item.out_order_id }}</div>
                        <div class="font-14 lh-20">{{ item.order_status_str }}</div>
                    </div>
                    <div class="flex flex-row space-between">
                        <div class="flex flex-row gap-6">
                            <div class="font-16 lh-22">{{ item.order_name }}</div>
                        </div>
                        <div class="font-14 lh-20">x{{ item.quantity }}</div>
                    </div>
                    <div class="flex flex-row space-between">
                        <div class="font-12 lh-16">{{ item.created_at }}</div>
                        <div class="flex flex-row baseline gap-2 color-black">
                            <div class="font-12 lh-16 flex">¥</div>
                            <div class="font-16 lh-16 font-weight-500">{{ formatPrice(item.actual_amount) }}</div>
                        </div>
                    </div>
                    <div class="flex flex-row gap-16 flex-end">
                        <div
                            class="small-default-btn"
                            v-if="isCanCancel(item.order_status)"
                            @click.stop="cancelOrder(item.out_order_id)"
                        >
                            取消订单
                        </div>
                        <div
                            class="small-primary-btn"
                            v-if="isCanRepay(item.order_status)"
                            @click.stop="rePay(item.out_order_id)"
                        >
                            立即支付
                        </div>
                    </div>
                </div>
            </van-list>
        </van-pull-refresh>
    </div>
</template>

<style lang="scss" scoped>
.order-list {
}
</style>
